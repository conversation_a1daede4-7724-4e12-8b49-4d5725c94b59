import { Paper, IconButton, Tooltip, Box } from "@mui/material";
import { observer } from "mobx-react-lite";
import { useLayoutStore } from "../store/store-context";
import UploadFileIcon from "@mui/icons-material/UploadFile";
import TextFieldsIcon from "@mui/icons-material/TextFields";
import VideocamIcon from "@mui/icons-material/Videocam";
import ImageIcon from "@mui/icons-material/Image";
import CategoryIcon from "@mui/icons-material/Category";
import AudiotrackIcon from "@mui/icons-material/Audiotrack";
import LayersIcon from "@mui/icons-material/Layers";
import React, { useState, useRef, useEffect, useCallback } from "react";
import { StoreContext } from "../store";
import ClosedCaptionIcon from "@mui/icons-material/ClosedCaption";
import DashboardCustomizeIcon from "@mui/icons-material/DashboardCustomize";
import GifBoxIcon from "@mui/icons-material/GifBox";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
const MenuList = observer(() => {
  const layoutStore = useLayoutStore();
  const store = React.useContext(StoreContext);

  // 滚动相关状态
  const [canScrollUp, setCanScrollUp] = useState(false);
  const [canScrollDown, setCanScrollDown] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 定义菜单项配置，使代码更简洁和易维护
  const menuItems = [
    // { id: "layers", icon: <LayersIcon />, option: "layers" },
    { id: "templates", icon: <DashboardCustomizeIcon />, option: "Templates" },
    { id: "uploads", icon: <UploadFileIcon />, option: "uploads" },

    { id: "videos", icon: <VideocamIcon />, option: "Video" },
    {
      id: "images",
      icon: <ImageIcon />,
      option: "Image",
    },
    { id: "audios", icon: <AudiotrackIcon />, option: "Audio" },
    { id: "texts", icon: <TextFieldsIcon />, option: "Text" },
    {
      id: "gifs",
      icon: <GifBoxIcon />,
      option: "Gif",
    },
    { id: "shapes", icon: <CategoryIcon />, option: "Shape" },
    { id: "captions", icon: <ClosedCaptionIcon />, option: "Caption" },
  ];

  // 检查滚动状态
  const checkScrollState = useCallback(() => {
    const container = scrollContainerRef.current;
    if (container) {
      const { scrollTop, scrollHeight, clientHeight } = container;
      setCanScrollUp(scrollTop > 0);
      setCanScrollDown(scrollTop < scrollHeight - clientHeight - 1);
    }
  }, []);

  // 滚动处理函数
  const handleScrollUp = () => {
    const container = scrollContainerRef.current;
    if (container) {
      container.scrollBy({ top: -48, behavior: "smooth" }); // 每次滚动一个按钮的高度
    }
  };

  const handleScrollDown = () => {
    const container = scrollContainerRef.current;
    if (container) {
      container.scrollBy({ top: 48, behavior: "smooth" }); // 每次滚动一个按钮的高度
    }
  };

  // 监听滚动事件和窗口大小变化
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      checkScrollState();
      container.addEventListener("scroll", checkScrollState);
      window.addEventListener("resize", checkScrollState);

      return () => {
        container.removeEventListener("scroll", checkScrollState);
        window.removeEventListener("resize", checkScrollState);
      };
    }
  }, [checkScrollState]);

  const handleMenuClick = (item: (typeof menuItems)[0]) => {
    if (item.option) {
      //@ts-ignore
      store.setSelectedMenuOption(item.option);
    }
    //@ts-ignore
    layoutStore.setActiveMenuItem(item.id);
    layoutStore.setShowMenuItem(true);
  };

  const getButtonStyle = (itemName: string) => {
    const isActive =
      layoutStore.showMenuItem && layoutStore.activeMenuItem === itemName;

    return {
      color: isActive ? "primary.main" : "text.secondary",
      bgcolor: isActive ? "action.selected" : "transparent",
      "&:hover": {
        bgcolor: "action.hover",
        color: isActive ? "primary.main" : "text.primary",
      },
      transition: "all 0.2s ease",
      my: 0.5,
    };
  };

  return (
    <Paper
      sx={{
        zIndex: 201,
        width: "60px",
        position: "absolute",
        top: "50%",
        transform: "translateY(-50%)",
        mt: 3,
        left: "16px",
        bgcolor: "grey.100",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        maxHeight: "calc(100vh - 200px)", // 限制最大高度
        overflow: "hidden",
      }}
    >
      {/* 向上滚动按钮 */}
      {canScrollUp && (
        <IconButton
          size="small"
          onClick={handleScrollUp}
          sx={{
            color: "text.secondary",
            "&:hover": {
              bgcolor: "action.hover",
              color: "text.primary",
            },
            transition: "all 0.2s ease",
            my: 0.25,
            minHeight: "32px",
            height: "32px",
          }}
        >
          <KeyboardArrowUpIcon fontSize="small" />
        </IconButton>
      )}

      {/* 可滚动的菜单项容器 */}
      <Box
        ref={scrollContainerRef}
        sx={{
          flex: 1,
          overflow: "auto",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          py: canScrollUp || canScrollDown ? 0.5 : 1.5,
          "&::-webkit-scrollbar": {
            display: "none", // 隐藏滚动条
          },
          scrollbarWidth: "none", // Firefox
          msOverflowStyle: "none", // IE
        }}
        onScroll={checkScrollState}
      >
        {menuItems.map((item) => (
          <Tooltip key={item.id} title={item.option} placement="right" arrow>
            <IconButton
              size="medium"
              onClick={() => handleMenuClick(item)}
              sx={getButtonStyle(item.id)}
            >
              {item.icon}
            </IconButton>
          </Tooltip>
        ))}
      </Box>

      {/* 向下滚动按钮 */}
      {canScrollDown && (
        <IconButton
          size="small"
          onClick={handleScrollDown}
          sx={{
            color: "text.secondary",
            "&:hover": {
              bgcolor: "action.hover",
              color: "text.primary",
            },
            transition: "all 0.2s ease",
            my: 0.25,
            minHeight: "32px",
            height: "32px",
          }}
        >
          <KeyboardArrowDownIcon fontSize="small" />
        </IconButton>
      )}
    </Paper>
  );
});

export default MenuList;
